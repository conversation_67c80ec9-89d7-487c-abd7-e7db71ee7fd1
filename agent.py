from agents import Agent, <PERSON>, SQLiteSession
from config import config
from output_type_pydantic import ContentOutput
from tools import post_to_x
import chainlit as cl


# -----------------------------
# Content Agent
# -----------------------------

content_agent = Agent(
    name="Content Agent",
    instructions=
     """You are an AI content creator specializing in Web3 adoption. 
        Given a detected user pain point, generate a short, clear, and reassuring reply. 
        Include a safe Blink CTA link and keep tone professional but engaging. 
        Return only JSON with keys: text, cta, tone.""",
        output_type=ContentOutput
        
)


# -----------------------------
# Engagement Agent (posting tool)
# -----------------------------

engagement_agent = Agent(
    name="Engagement Agent",
    instructions="Take content from Content Agent and post it using the post_to_x tool.",
    tools=[post_to_x]
)


# -----------------------------
# Triage Agent
# -----------------------------

triage_agent = Agent(
    name="Triage Agent",
    instructions=(
        "Classify an incoming social post into one of: Onboarding, Safety, Confusion, Utility, Overload. "
        "If classification is Onboarding or Safety, hand off to Content Agent. "
        "If classification is Utility, hand off to Engagement Agent. "
        "Otherwise, respond with 'No action needed'."
    ),
    handoffs=[content_agent, engagement_agent]
)



session_history = SQLiteSession("user1", "conversion.db")


# while True:
#     prompt = input("User: ")
#     if prompt == "exit":
#         break

#     result = Runner.run_sync(
#         starting_agent=triage_agent,
#         input=prompt,
#         session=session_history,
#         run_config=config
#     )

#     print("Agent:", result.final_output)


@cl.on_chat_start
async def start_chat():
    # har user ke liye ek khali history list set karo
    cl.user_session.set("history", [])
    await cl.Message(content="👋 Hi! Type any pain point and I’ll generate a reply + CTA.").send()

@cl.on_message
async def handle_message(message: cl.Message):
    user_input = message.content.strip()

    # history list nikalo
    history = cl.user_session.get("history", [])

    result = await Runner.run(
        starting_agent=triage_agent,
        input=user_input,
        run_config=config
    )

    reply = str(result.final_output)

    await cl.Message(content=reply).send()