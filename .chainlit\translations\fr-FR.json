{"common": {"actions": {"cancel": "Annuler", "confirm": "Confirmer", "continue": "<PERSON><PERSON><PERSON>", "goBack": "Retour", "reset": "Réinitialiser", "submit": "Envoyer"}, "status": {"loading": "Chargement...", "error": {"default": "Une erreur est survenue", "serverConnection": "Impossible de joindre le serveur"}}}, "auth": {"login": {"title": "Connectez-vous pour accéder à l'application", "form": {"email": {"label": "Adresse e-mail", "required": "l'e-mail est un champ obligatoire", "placeholder": "<EMAIL>"}, "password": {"label": "Mot de passe", "required": "le mot de passe est un champ obligatoire"}, "actions": {"signin": "Se connecter"}, "alternativeText": {"or": "OU"}}, "errors": {"default": "Impossible de se connecter", "signin": "Essayez de vous connecter avec un autre compte", "oauthSignin": "Essayez de vous connecter avec un autre compte", "redirectUriMismatch": "L'URI de redirection ne correspond pas à la configuration de l'application oauth", "oauthCallback": "Essayez de vous connecter avec un autre compte", "oauthCreateAccount": "Essayez de vous connecter avec un autre compte", "emailCreateAccount": "Essayez de vous connecter avec un autre compte", "callback": "Essayez de vous connecter avec un autre compte", "oauthAccountNotLinked": "Pour confirmer votre identité, connectez-vous avec le même compte que vous avez utilisé à l'origine", "emailSignin": "L'e-mail n'a pas pu être envoyé", "emailVerify": "Veuillez vérifier votre e-mail, un nouvel e-mail a été envoyé", "credentialsSignin": "La connexion a échoué. Vérifiez que les informations que vous avez fournies sont correctes", "sessionRequired": "Veuillez vous connecter pour accéder à cette page"}}, "provider": {"continue": "Continuer avec {{provider}}"}}, "chat": {"input": {"placeholder": "Tapez votre message ici...", "actions": {"send": "Envoyer le message", "stop": "<PERSON><PERSON><PERSON><PERSON> tâche", "attachFiles": "Jo<PERSON>re des fichiers"}}, "commands": {"button": "Outils", "changeTool": "Changer d'outil", "availableTools": "Outils disponibles"}, "speech": {"start": "Démarrer l'enregistrement", "stop": "<PERSON><PERSON><PERSON><PERSON> l'enregistrement", "connecting": "Connexion en cours"}, "fileUpload": {"dragDrop": "Glissez et déposez des fichiers ici", "browse": "Parcourir les fichiers", "sizeLimit": "Limite :", "errors": {"failed": "Échec du téléversement", "cancelled": "Téléversement annulé de"}, "actions": {"cancelUpload": "Annuler le téléversement", "removeAttachment": "Supprimer la pièce jointe"}}, "messages": {"status": {"using": "Utilise", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"copy": {"button": "Copier dans le presse-papiers", "success": "Copié !"}}, "feedback": {"positive": "Utile", "negative": "Pas utile", "edit": "Modifier le commentaire", "dialog": {"title": "Ajouter un commentaire", "submit": "Envoyer le commentaire", "yourFeedback": "Votre avis..."}, "status": {"updating": "Mise à jour", "updated": "Commentaire mis à jour"}}}, "history": {"title": "Dernières entrées", "empty": "Tellement vide...", "show": "Afficher l'historique"}, "settings": {"title": "Panneau des paramètres", "customize": "Personnalisez vos paramètres de chat ici"}, "watermark": "Construit avec"}, "threadHistory": {"sidebar": {"title": "Discussions passées", "filters": {"search": "<PERSON><PERSON><PERSON>", "placeholder": "Rechercher des conversations..."}, "timeframes": {"today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "previous7days": "Les 7 derniers jours", "previous30days": "Les 30 derniers jours"}, "empty": "Aucun fil de discussion trouvé", "actions": {"close": "<PERSON><PERSON><PERSON> la barre latérale", "open": "<PERSON>u<PERSON><PERSON>r la barre latérale"}}, "thread": {"untitled": "Conversation sans titre", "menu": {"rename": "<PERSON>mmer", "share": "Partager", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"share": {"title": "Partager le lien de la discussion", "button": "Partager", "status": {"copied": "<PERSON>n copié", "created": "Lien de partage créé !", "unshared": "Partage désactivé pour ce fil"}, "error": {"create": "Échec de la création du lien de partage", "unshare": "Échec de la désactivation du partage du fil"}}, "delete": {"title": "Confirmer la <PERSON>", "description": "Cela supprimera le fil de discussion ainsi que ses messages et éléments. Cette action ne peut pas être annulée", "success": "Discussion supprimée", "inProgress": "Suppression de la discussion"}, "rename": {"title": "Renommer le fil de discussion", "description": "Entrez un nouveau nom pour ce fil de discussion", "form": {"name": {"label": "Nom", "placeholder": "Entrez le nouveau nom"}}, "success": "Fil de discussion renommé !", "inProgress": "Renommage du fil de discussion"}}}}, "navigation": {"header": {"chat": "Discussion", "readme": "Lisez-moi", "theme": {"light": "Thème clair", "dark": "Thème sombre", "system": "Suivre le système"}}, "newChat": {"button": "Nouvelle discussion", "dialog": {"title": "<PERSON><PERSON>er une nouvelle discussion", "description": "<PERSON><PERSON> effacera votre historique de discussion actuel. Êtes-vous sûr de vouloir continuer ?", "tooltip": "Nouvelle discussion"}}, "user": {"menu": {"settings": "Paramètres", "settingsKey": "S", "apiKeys": "Clés API", "logout": "Se déconnecter"}}}, "apiKeys": {"title": "Clés API requises", "description": "Pour utiliser cette application, les clés API suivantes sont requises. Les clés sont stockées dans le stockage local de votre appareil.", "success": {"saved": "Enregistré avec succès"}}, "alerts": {"info": "Info", "note": "Note", "tip": "Astuce", "important": "Important", "warning": "Avertissement", "caution": "Attention", "debug": "Débogage", "example": "Exemple", "success": "Su<PERSON>ès", "help": "Aide", "idea": "<PERSON><PERSON><PERSON>", "pending": "En attente", "security": "Sécurité", "beta": "<PERSON><PERSON><PERSON>", "best-practice": "<PERSON><PERSON><PERSON> pratique"}, "components": {"MultiSelectInput": {"placeholder": "Sélectionner..."}}}